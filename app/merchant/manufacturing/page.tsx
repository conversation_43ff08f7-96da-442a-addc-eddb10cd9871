"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Wrench,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  Eye,
  Plus,
  Calendar,
  Package,
  Settings,
} from "lucide-react";
import { GroupPurchase, ManufacturingUpdate } from "@/types/merchant";
import { formatZAR, convertToZAR } from "@/data/south-african-context";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import Image from "next/image";

// South African manufacturing data with product images
const mockManufacturingGroups: (GroupPurchase & {
  manufacturingProgress: number;
  currentPhase: string;
  estimatedCompletion: Date;
  lastUpdate?: Date;
  productName: string;
  productImage: string;
  category: string;
})[] = [
  {
    id: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: "Memorial for John Smith",
    adminId: "u1",
    totalMembers: 8,
    targetAmount: convertToZAR(3200),
    currentAmount: convertToZAR(2880),
    paymentProgress: 90,
    status: "manufacturing",
    createdAt: new Date("2024-01-15"),
    manufacturingStarted: new Date("2024-01-25"),
    manufacturingProgress: 65,
    currentPhase: "Stone Cutting",
    estimatedCompletion: new Date("2024-02-15"),
    lastUpdate: new Date("2024-02-01"),
    productName: "Classic Granite Memorial",
    productImage: getStoneImagesForCategory("traditional", 1)[0],
    category: "traditional",
  },
  {
    id: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: "Family Heritage Stone",
    adminId: "u2",
    totalMembers: 12,
    targetAmount: convertToZAR(4500),
    currentAmount: convertToZAR(3600),
    paymentProgress: 80,
    status: "manufacturing",
    createdAt: new Date("2024-01-10"),
    manufacturingStarted: new Date("2024-01-20"),
    manufacturingProgress: 85,
    currentPhase: "Final Polish",
    estimatedCompletion: new Date("2024-02-10"),
    lastUpdate: new Date("2024-02-03"),
    productName: "Heritage Family Stone",
    productImage: getStoneImagesForCategory("premium", 1)[0],
    category: "premium",
  },
  {
    id: "g3",
    productId: "p3",
    merchantId: "m1",
    groupName: "Custom Memorial Design",
    adminId: "u3",
    totalMembers: 6,
    targetAmount: convertToZAR(5200),
    currentAmount: convertToZAR(4160),
    paymentProgress: 80,
    status: "collecting", // Ready to start manufacturing
    createdAt: new Date("2024-01-20"),
    manufacturingProgress: 0,
    currentPhase: "Ready to Start",
    estimatedCompletion: new Date("2024-03-15"),
    productName: "Custom Memorial Design",
    productImage: getStoneImagesForCategory("custom", 1)[0],
    category: "custom",
  },
  {
    id: "g4",
    productId: "p1",
    merchantId: "m1",
    groupName: "Community Memorial Garden",
    adminId: "u4",
    totalMembers: 15,
    targetAmount: convertToZAR(8500),
    currentAmount: convertToZAR(8500),
    paymentProgress: 100,
    status: "manufacturing",
    createdAt: new Date("2024-01-05"),
    manufacturingStarted: new Date("2024-01-15"),
    manufacturingCompleted: new Date("2024-02-05"),
    manufacturingProgress: 100,
    currentPhase: "Completed",
    estimatedCompletion: new Date("2024-02-05"),
    lastUpdate: new Date("2024-02-05"),
    productName: "Community Memorial Garden",
    productImage: getStoneImagesForCategory("modern", 1)[0],
    category: "modern",
  },
];

const mockRecentUpdates: ManufacturingUpdate[] = [
  {
    id: "mu1",
    groupId: "g1",
    phaseId: "ph2",
    merchantId: "m1",
    title: "Stone Cutting Progress",
    description:
      "Stone cutting is 65% complete. The granite has been shaped according to specifications.",
    images: [getStoneImagesForCategory("traditional", 2)[1]],
    status: "in_progress",
    completionPercentage: 65,
    estimatedCompletion: new Date("2024-02-15"),
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-02-01"),
  },
  {
    id: "mu2",
    groupId: "g2",
    phaseId: "ph4",
    merchantId: "m1",
    title: "Final Polish Started",
    description:
      "Beginning the final polishing phase. The engraving work has been completed successfully.",
    images: [getStoneImagesForCategory("premium", 2)[1]],
    status: "in_progress",
    completionPercentage: 85,
    estimatedCompletion: new Date("2024-02-10"),
    createdAt: new Date("2024-02-03"),
    updatedAt: new Date("2024-02-03"),
  },
];

export default function MerchantManufacturing() {
  const [manufacturingGroups, setManufacturingGroups] = useState(
    mockManufacturingGroups
  );
  const [filteredGroups, setFilteredGroups] = useState(mockManufacturingGroups);
  const [recentUpdates, setRecentUpdates] = useState(mockRecentUpdates);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  useEffect(() => {
    let filtered = manufacturingGroups;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter((group) =>
        group.groupName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      switch (statusFilter) {
        case "ready_to_start":
          filtered = filtered.filter(
            (group) =>
              group.paymentProgress >= 80 && group.status === "collecting"
          );
          break;
        case "in_progress":
          filtered = filtered.filter(
            (group) =>
              group.status === "manufacturing" &&
              group.manufacturingProgress < 100
          );
          break;
        case "completed":
          filtered = filtered.filter(
            (group) => group.manufacturingProgress === 100
          );
          break;
        case "delayed":
          // Add logic for delayed items based on estimated completion dates
          filtered = filtered.filter(
            (group) =>
              group.estimatedCompletion < new Date() &&
              group.manufacturingProgress < 100
          );
          break;
      }
    }

    setFilteredGroups(filtered);
  }, [manufacturingGroups, searchTerm, statusFilter]);

  const getStatusColor = (group: (typeof mockManufacturingGroups)[0]) => {
    if (group.manufacturingProgress === 100)
      return "bg-green-100 text-green-800";
    if (group.status === "manufacturing") return "bg-blue-100 text-blue-800";
    if (group.paymentProgress >= 80) return "bg-orange-100 text-orange-800";
    return "bg-gray-100 text-gray-800";
  };

  const getStatusText = (group: (typeof mockManufacturingGroups)[0]) => {
    if (group.manufacturingProgress === 100) return "Completed";
    if (group.status === "manufacturing") return "In Progress";
    if (group.paymentProgress >= 80) return "Ready to Start";
    return "Waiting for Payment";
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return "bg-green-500";
    if (progress >= 50) return "bg-blue-500";
    return "bg-yellow-500";
  };

  // Calculate summary statistics
  const readyToStart = filteredGroups.filter(
    (g) => g.paymentProgress >= 80 && g.status === "collecting"
  ).length;
  const inProgress = filteredGroups.filter(
    (g) => g.status === "manufacturing" && g.manufacturingProgress < 100
  ).length;
  const completed = filteredGroups.filter(
    (g) => g.manufacturingProgress === 100
  ).length;
  const averageProgress =
    filteredGroups.reduce(
      (sum, group) => sum + group.manufacturingProgress,
      0
    ) / filteredGroups.length || 0;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Manufacturing</h1>
        <p className="text-muted-foreground">
          Track and manage manufacturing progress for all groups
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Ready to Start
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{readyToStart}</div>
            <p className="text-xs text-muted-foreground">
              80%+ payment collected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Wrench className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgress}</div>
            <p className="text-xs text-muted-foreground">
              Currently manufacturing
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completed}</div>
            <p className="text-xs text-muted-foreground">
              Ready for installation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Average Progress
            </CardTitle>
            <Package className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {averageProgress.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">Across all projects</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search groups..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="ready_to_start">Ready to Start</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="delayed">Delayed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Manufacturing Groups */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Manufacturing Projects</CardTitle>
              <CardDescription>
                Current and upcoming manufacturing projects
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {filteredGroups.map((group) => (
                <div key={group.id} className="border rounded-lg p-4">
                  <div className="flex items-start space-x-4 mb-3">
                    <div className="flex-shrink-0">
                      <Image
                        src={group.productImage}
                        alt={group.productName}
                        width={64}
                        height={64}
                        className="rounded-lg object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-semibold">{group.groupName}</h4>
                          <p className="text-sm text-muted-foreground">
                            {group.productName} • Current Phase:{" "}
                            {group.currentPhase}
                          </p>
                        </div>
                        <Badge className={getStatusColor(group)}>
                          {getStatusText(group)}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2 mb-3">
                    <div className="flex justify-between text-sm">
                      <span>Manufacturing Progress</span>
                      <span>{group.manufacturingProgress}%</span>
                    </div>
                    <Progress
                      value={group.manufacturingProgress}
                      className="h-2"
                    />
                  </div>

                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          Due:{" "}
                          {group.estimatedCompletion.toLocaleDateString(
                            "en-ZA"
                          )}
                        </span>
                      </div>
                      {group.lastUpdate && (
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>
                            Updated:{" "}
                            {group.lastUpdate.toLocaleDateString("en-ZA")}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      {group.paymentProgress >= 80 &&
                        group.status === "collecting" && (
                          <Button size="sm" variant="outline">
                            <Play className="mr-1 h-3 w-3" />
                            Start
                          </Button>
                        )}
                      <Link href={`/merchant/manufacturing/${group.id}`}>
                        <Button size="sm" variant="outline">
                          <Eye className="mr-1 h-3 w-3" />
                          View
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              ))}

              {filteredGroups.length === 0 && (
                <div className="text-center py-8">
                  <Wrench className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    No manufacturing projects found.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Updates */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Updates</CardTitle>
              <CardDescription>
                Latest manufacturing progress updates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentUpdates.map((update) => (
                <div key={update.id} className="border rounded-lg p-3">
                  <div className="flex items-start space-x-3 mb-2">
                    <div className="flex-shrink-0">
                      <Image
                        src={update.images[0]}
                        alt={update.title}
                        width={48}
                        height={48}
                        className="rounded-lg object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h5 className="font-medium text-sm">{update.title}</h5>
                        <Badge variant="outline" className="text-xs">
                          {update.completionPercentage}%
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mb-2">
                        {update.description}
                      </p>
                      <div className="text-xs text-muted-foreground">
                        {update.createdAt.toLocaleDateString("en-ZA")}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              <Button variant="outline" className="w-full" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Update
              </Button>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link href="/merchant/manufacturing/phases">
                <Button variant="outline" className="w-full" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  Manage Global Phases
                </Button>
              </Link>
              <Button variant="outline" className="w-full" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Bulk Update Progress
              </Button>
              <Button variant="outline" className="w-full" size="sm">
                <Calendar className="mr-2 h-4 w-4" />
                Schedule Installation
              </Button>
              <Button variant="outline" className="w-full" size="sm">
                <Package className="mr-2 h-4 w-4" />
                Export Report
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
